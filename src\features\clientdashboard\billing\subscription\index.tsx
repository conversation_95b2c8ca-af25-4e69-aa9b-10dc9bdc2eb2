import { BackButton } from "@/_components/common";
import { useEffect, useState } from "react";
import {
  getSubscriptionDetails,
  getUserProfile,
} from "../billing_api/billingapi";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { format } from "date-fns";
import CancelSubscriptionDialog from "./canclesubscription"; // Import the dialog component

export default function Subscription() {
  const [subscriptions, setSubscriptions] = useState<any[]>([]);
  const [userProfile, setUserProfile] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [profileLoading, setProfileLoading] = useState(true);
  const [selectedSubscription, setSelectedSubscription] = useState<any>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isCancelDialogOpen, setIsCancelDialogOpen] = useState(false);
  const [cancellingSubscription, setCancellingSubscription] =
    useState<any>(null);
  // const [cancelling, setCancelling] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setProfileLoading(true);

        // First fetch subscriptions
        const subscriptionsResponse = await getSubscriptionDetails();
        if (subscriptionsResponse?.status === 1) {
          // Filter to get only ACTIVE subscriptions
          const activeSubscriptions = subscriptionsResponse.data.filter(
            (sub: any) => sub.status === "ACTIVE"
          );
          setSubscriptions(activeSubscriptions);
        }

        // Then fetch user profile
        const profileResponse = await getUserProfile();
        if (profileResponse?.status === 1) {
          setUserProfile(profileResponse.data);
        }
      } catch (error) {
        console.error("Error:", error);
      } finally {
        setLoading(false);
        setProfileLoading(false);
      }
    };

    fetchData();
  }, []);

  const formatDate = (dateString: string) => {
    if (!dateString) return "N/A";
    return format(new Date(dateString), "dd/MM/yyyy");
  };

  const openDetailsModal = (subscription: any) => {
    setSelectedSubscription(subscription);
    setIsDialogOpen(true);
  };

  const openCancelDialog = (subscription: any) => {
    setCancellingSubscription(subscription);
    setIsCancelDialogOpen(true);
  };

  const handleCancelSubscription = async () => {
    if (!cancellingSubscription) return;

    try {
      // setCancelling(true);
      // API integration commented out as requested
      /*
      const response = await cancelSubscription(cancellingSubscription.id);
      
      if (response?.status === 1) {
        // Update the subscriptions list by removing the cancelled one
        setSubscriptions(subscriptions.filter(
          sub => sub.id !== cancellingSubscription.id
        ));
        
        // Close the dialogs
        setIsCancelDialogOpen(false);
        setIsDialogOpen(false);
      } else {
        console.error("Failed to cancel subscription:", response?.message);
        alert("Failed to cancel subscription. Please try again.");
      }
      */

      // Simulate API call with timeout
      setTimeout(() => {
        // For demo purposes, we'll just remove the subscription from the list
        setSubscriptions(
          subscriptions.filter((sub) => sub.id !== cancellingSubscription.id)
        );

        // Close the dialogs
        setIsCancelDialogOpen(false);
        setIsDialogOpen(false);
        // setCancelling(false);
        setCancellingSubscription(null);

        console.log(
          "Subscription cancellation simulated for:",
          cancellingSubscription.id
        );
      }, 1500);
    } catch (error) {
      console.error("Error cancelling subscription:", error);
      alert("An error occurred while cancelling the subscription.");
      // setCancelling(false);
    }
  };

  return (
    <div className="max-w-full mx-8">
      <div className="flex flex-row mb-6 items-center">
        <BackButton />
        <h2 className="text-2xl font-bold text-[#292929]">
          Subscription Details
        </h2>
      </div>

      {/* Payment Details Section */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">Payment Details</h3>
        <div className="bg-red-50 border border-[#FF577F] rounded-md p-6 flex justify-between items-start">
          <div>
            <p className="font-semibold text-[#5E5E5E] text-[18px]">
              {profileLoading
                ? "Loading..."
                : userProfile
                ? `${userProfile.name}${
                    userProfile.lastname ? ` ${userProfile.lastname}` : ""
                  }`
                : "User Name"}
            </p>
            <p className="text-[#5E5E5E] text-sm">View Visa Card</p>
          </div>
          <button className="border border-[#FF577F] text-[#FF577F] px-4 py-2 rounded-md hover:bg-pink-100">
            Update Payment Details
          </button>
        </div>
      </div>

      {/* Current Plan Section - Now showing only ACTIVE subscriptions */}
      <div className="mb-8">
        <h3 className="text-[20px] font-semibold mb-4 text-[#282828]">
          Current Plans
        </h3>

        {loading ? (
          <div>Loading...</div>
        ) : subscriptions.length > 0 ? (
          <div className="space-y-4">
            {subscriptions.map((subscription, index) => (
              <div
                key={index}
                className="bg-white border border-[#FF577F] rounded-md p-6 flex justify-between items-start shadow-sm relative"
              >
                {/* Active Tag */}
                <div className="absolute -top-2 -left-2">
                  <span className={`bg-${subscription.status === "ACTIVE" ? "green" : "red"}-500 text-white text-xs font-semibold px-2 py-1 rounded-md`}>
                    {/* <span className="bg-green-500 text-white text-xs font-semibold px-2 py-1 rounded-md"> */}
                    {subscription.status === "ACTIVE" ? "ACTIVE" : "INACTIVE"}
                  </span>
                </div>

                <div>
                  <p className="font-semibold lg:text-[18px] text-[15px] text-[#5E5E5E]">
                    {subscription.package.name}
                    <span className="font-bold ml-3">
                      ${subscription.package.price}
                    </span>{" "}
                    {/* | {subscription.package.members} members */}
                  </p>
                  <p className="text-sm text-gray-600 mt-2">
                    Active until {formatDate(subscription.endDate)}
                    <span
                      className="text-[#FF577F] ml-2 cursor-pointer"
                      onClick={() => openCancelDialog(subscription)}
                    >
                      Cancel Subscription
                    </span>
                  </p>
                </div>
                <button
                  onClick={() => openDetailsModal(subscription)}
                  className="border border-[#FF577F] text-[#FF577F] px-8 py-2 rounded-md hover:bg-pink-100"
                >
                  View Plan Details
                </button>
              </div>
            ))}
          </div>
        ) : (
          <div className="bg-white border border-gray-200 rounded-md p-6 text-center">
            <p className="text-gray-500">No active subscriptions found</p>
          </div>
        )}
      </div>

      {/* Details Dialog Modal */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="text-[#FF577F] text-[25px]">
              Subscription Details
            </DialogTitle>
          </DialogHeader>
          {selectedSubscription && (
            <div className="grid gap-4 py-4">
              {/* Status Badge */}
              <div className="flex justify-end">
                <span className="bg-green-500 text-white text-sm font-semibold px-3 py-1 rounded-md">
                  {selectedSubscription.status}
                </span>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-500">
                    Package Name
                  </h4>
                  <p className="text-sm mt-1 py-3 px-3 rounded-xl border-gradient bg-[#F9EFEF]">
                    {selectedSubscription.package.name}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Price</h4>
                  <p className="text-sm mt-1 py-3 px-3 rounded-xl border-gradient bg-[#F9EFEF]">
                    ₹{selectedSubscription.package.price}
                  </p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-500">
                    Start Date
                  </h4>
                  <p className="text-sm mt-1 py-3 px-3 rounded-xl border-gradient bg-[#F9EFEF]">
                    {formatDate(selectedSubscription.startDate)}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">
                    End Date
                  </h4>
                  <p className="text-sm mt-1 py-3 px-3 rounded-xl border-gradient bg-[#F9EFEF]">
                    {formatDate(selectedSubscription.endDate)}
                  </p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-500">
                    Next Billing Date
                  </h4>
                  <p className="text-sm mt-1 py-3 px-3 rounded-xl border-gradient bg-[#F9EFEF]">
                    {formatDate(selectedSubscription.next_billing_date)}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Status</h4>
                  <p className="text-sm mt-1 py-3 px-3 rounded-xl border-gradient bg-[#F9EFEF]">
                    {selectedSubscription.status}
                  </p>
                </div>
              </div>
              <div className="mt-4 flex justify-end">
                <button
                  onClick={() => openCancelDialog(selectedSubscription)}
                  className="border border-[#FF577F] text-[#FF577F] px-6 py-2 rounded-md hover:bg-pink-100"
                >
                  Cancel Subscription
                </button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Cancel Subscription Dialog */}
      <CancelSubscriptionDialog
        isOpen={isCancelDialogOpen}
        onClose={() => {
          setIsCancelDialogOpen(false);
          setCancellingSubscription(null);
        }}
        onConfirm={handleCancelSubscription}
        subscriptionName={cancellingSubscription?.package?.name || ""}
        subscriptionId={cancellingSubscription?.id}
      />
    </div>
  );
}
