{"root": ["./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/_components/common/backbutton.tsx", "./src/_components/common/bordergradientexample.tsx", "./src/_components/common/grafientbutton.tsx", "./src/_components/common/scrolltotop.tsx", "./src/_components/common/bordergradient.tsx", "./src/_components/common/customtoast.tsx", "./src/_components/common/index.ts", "./src/_components/common/nodata.tsx", "./src/components/annotator/components/notificaitondata.tsx", "./src/components/annotator/components/notification.tsx", "./src/components/annotator/router/index.tsx", "./src/components/attendance/attendancetemplate.tsx", "./src/components/attendance/downloadattendancebutton.tsx", "./src/components/globalfiles/data.table.tsx", "./src/components/globalfiles/debounce.tsx", "./src/components/globalfiles/layout.tsx", "./src/components/globalfiles/loader.tsx", "./src/components/globalfiles/pagination.tsx", "./src/components/globalfiles/usepagination.tsx", "./src/components/invoice/invoicetemplate.tsx", "./src/components/loaders/loaders.one.tsx", "./src/components/misc/not-found.tsx", "./src/components/pdf-test/pdftestpage.tsx", "./src/components/ui/accordion.tsx", "./src/components/ui/badge.tsx", "./src/components/ui/button.tsx", "./src/components/ui/card.tsx", "./src/components/ui/chart.tsx", "./src/components/ui/checkbox.tsx", "./src/components/ui/collapsible.tsx", "./src/components/ui/command.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/dropdown-menu.tsx", "./src/components/ui/file-upload.tsx", "./src/components/ui/form.tsx", "./src/components/ui/input-otp.tsx", "./src/components/ui/input.tsx", "./src/components/ui/label.tsx", "./src/components/ui/popover.tsx", "./src/components/ui/radio-group.tsx", "./src/components/ui/responsive-background.tsx", "./src/components/ui/responsive-image.tsx", "./src/components/ui/scroll-area.tsx", "./src/components/ui/select.tsx", "./src/components/ui/separator.tsx", "./src/components/ui/sheet.tsx", "./src/components/ui/sidebar.tsx", "./src/components/ui/skeleton.tsx", "./src/components/ui/switch.tsx", "./src/components/ui/table.tsx", "./src/components/ui/textarea.tsx", "./src/components/ui/toast.tsx", "./src/components/ui/toaster.tsx", "./src/components/ui/tooltip.tsx", "./src/context/matchmaking.context.tsx", "./src/context/roles.context.tsx", "./src/features/coworker/auth/authcoworker.tsx", "./src/features/coworker/components/validate.signup.tsx", "./src/features/admindashboard/index.tsx", "./src/features/admindashboard/admin-list/index.tsx", "./src/features/admindashboard/admin-list/adminlist_api/adminlist_api.tsx", "./src/features/admindashboard/admin-list/adminlist_api/adminlist_mutations.ts", "./src/features/admindashboard/admin-list/adminlist_api/useadminquery.ts", "./src/features/admindashboard/admin-list/components/admincolumn.tsx", "./src/features/admindashboard/admin-list/components/attendance.log.tsx", "./src/features/admindashboard/admin-list/components/attendancetype.tsx", "./src/features/admindashboard/admin-list/modal/createadminmodal.tsx", "./src/features/admindashboard/admin-list/modal/reactive.tsx", "./src/features/admindashboard/admin-list/modal/resetpasswordmodal.tsx", "./src/features/admindashboard/admin-list/modal/suspended.tsx", "./src/features/admindashboard/adminnotification/index.tsx", "./src/features/admindashboard/adminnotification/leaveprrovalrejectfile.tsx", "./src/features/admindashboard/adminnotification/shiftaprrovalrejectfile.tsx", "./src/features/admindashboard/adminnotification/notification_api/adminnotification_api.tsx", "./src/features/admindashboard/api/api.tsx", "./src/features/admindashboard/bankothertable/index.tsx", "./src/features/admindashboard/bankothertable/bankouthetable/bankcolumn.tsx", "./src/features/admindashboard/bankothertable/bankouthetable/bankdetails.type.tsx", "./src/features/admindashboard/bankothertable/bankouthetable/bankother.log.tsx", "./src/features/admindashboard/bankothertable/bankouthetable/bankothertable.tsx", "./src/features/admindashboard/bankothertable/bankouthetable/dummnydata.tsx", "./src/features/admindashboard/bankothertable/banktransferapi/imagemodal.tsx", "./src/features/admindashboard/bankothertable/banktransferapi/bank_transfer_api.tsx", "./src/features/admindashboard/bankothertable/historybankdetails/historybankcolumn.tsx", "./src/features/admindashboard/bankothertable/historybankdetails/historybankothertable.tsx", "./src/features/admindashboard/components/admindashboard.tsx", "./src/features/admindashboard/components/adminfaqs.tsx", "./src/features/admindashboard/components/adminkanban.tsx", "./src/features/admindashboard/components/admintask.tsx", "./src/features/admindashboard/components/columncontainer.tsx", "./src/features/admindashboard/components/draggabletask.tsx", "./src/features/admindashboard/components/settingadmin.tsx", "./src/features/admindashboard/components/updatetask.tsx", "./src/features/admindashboard/components/common/confirmmodalsetting.tsx", "./src/features/admindashboard/components/common/deletemodal.tsx", "./src/features/admindashboard/components/common/deletrsetting.tsx", "./src/features/admindashboard/components/common/faqsmodal.tsx", "./src/features/admindashboard/components/common/otpmodalsetting.tsx", "./src/features/admindashboard/components/faq_setting_api/faq_api/faqsapi.tsx", "./src/features/admindashboard/components/faq_setting_api/setting/otpdelete.tsx", "./src/features/admindashboard/components/faq_setting_api/setting/setting_api.tsx", "./src/features/admindashboard/components/styles/admindashboardstyles.ts", "./src/features/admindashboard/detailsadmin/admintaskdetails.tsx", "./src/features/admindashboard/detailsadmin/adminannotator.tsx", "./src/features/admindashboard/detailsadmin/adminclients.tsx", "./src/features/admindashboard/detailsadmin/adminprojectdesc.tsx", "./src/features/admindashboard/detailsadmin/adminprojectkanban.tsx", "./src/features/admindashboard/detailsadmin/admintask.tsx", "./src/features/admindashboard/detailsadmin/admindetails_api/admindetails_api.tsx", "./src/features/admindashboard/detailsadmin/annonatoradmin/adminshiftchange.tsx", "./src/features/admindashboard/detailsadmin/annonatoradmin/index.tsx", "./src/features/admindashboard/detailsadmin/annonatoradmin/components/adminannotatorcard.tsx", "./src/features/admindashboard/detailsadmin/clientadmin/index.tsx", "./src/features/admindashboard/detailsadmin/clientadmin/api/useclientprojectsquery.ts", "./src/features/admindashboard/detailsadmin/clientadmin/components/admincolumn.tsx", "./src/features/admindashboard/detailsadmin/clientadmin/components/clientadmincard.tsx", "./src/features/admindashboard/detailsadmin/clientadmin/components/attendance.log.tsx", "./src/features/admindashboard/detailsadmin/clientadmin/components/index.tsx", "./src/features/admindashboard/detailsadmin/clientadmin/components/adminclientprofile/adminclientbilling.tsx", "./src/features/admindashboard/detailsadmin/clientadmin/components/adminclientprofile/adminclientprofile.tsx", "./src/features/admindashboard/detailsadmin/clientadmin/components/adminclientprofile/adminclientprofile_api.tsx", "./src/features/admindashboard/detailsadmin/clientadmin/components/adminclientprofile/adminprofiledetials.tsx", "./src/features/admindashboard/detailsadmin/clientadmin/components/adminclientsuspenddelete_api/adminclientssuspenddelete_api.tsx", "./src/features/admindashboard/detailsadmin/clientadmin/components/modal/deleteadminclient.tsx", "./src/features/admindashboard/detailsadmin/clientadmin/components/modal/editadminclient.tsx", "./src/features/admindashboard/detailsadmin/clientadmin/components/modal/reactiveadminclient.tsx", "./src/features/admindashboard/detailsadmin/clientadmin/components/modal/suspendadminclient.tsx", "./src/features/admindashboard/detailsadmin/coodinatoradmin/index.tsx", "./src/features/admindashboard/detailsadmin/coodinatoradmin/api/usecoordinatorclientsquery.ts", "./src/features/admindashboard/detailsadmin/coodinatoradmin/components/admincolumn.tsx", "./src/features/admindashboard/detailsadmin/coodinatoradmin/components/coordinatorcard.tsx", "./src/features/admindashboard/detailsadmin/coodinatoradmin/components/attendance.log.tsx", "./src/features/admindashboard/detailsadmin/projectadmin/adminupdateproejcttask.tsx", "./src/features/admindashboard/detailsadmin/projectadmin/detailscolumncontainer.tsx", "./src/features/admindashboard/detailsadmin/projectadmin/detailscreateproejcttask.tsx", "./src/features/admindashboard/detailsadmin/projectadmin/detailsdraggabletask.tsx", "./src/features/admindashboard/detailsadmin/projectadmin/index.tsx", "./src/features/admindashboard/detailsadmin/projectadmin/projectdetailskanban.tsx", "./src/features/admindashboard/detailsadmin/projectadmin/components/questionnaire.tsx", "./src/features/admindashboard/detailsadmin/projectadmin/components/project-create.tsx", "./src/features/admindashboard/detailsadmin/projectadmin/components/projectdata.tsx", "./src/features/admindashboard/detailsadmin/projectadmin/project_api/project_api.tsx", "./src/features/admindashboard/detailsadmin/projectadmin/styles/projectadminstyles.ts", "./src/features/admindashboard/detailsadmin/projectadmin/styles/projectcreatestyles.ts", "./src/features/admindashboard/matchmaking/index.tsx", "./src/features/admindashboard/matchmaking/api/matchmaking.api.ts", "./src/features/admindashboard/matchmaking/api/mutation.ts", "./src/features/admindashboard/matchmaking/api/query.ts", "./src/features/admindashboard/matchmaking/components/admincolumn.tsx", "./src/features/admindashboard/matchmaking/components/attendance.log.tsx", "./src/features/admindashboard/matchmaking/components/questionairedetails/matchmaking.types.ts", "./src/features/admindashboard/matchmaking/components/questionairedetails/questioniredata.tsx", "./src/features/admindashboard/matchmaking/components/questionairedetails/questioniredetails.tsx", "./src/features/admindashboard/matchmaking/components/questionairedetails/questionire_api/questionire_api.tsx", "./src/features/admindashboard/onboard/attendance.log.tsx", "./src/features/admindashboard/onboard/types.ts", "./src/features/admindashboard/onboard/api/mutation.ts", "./src/features/admindashboard/onboard/api/onboarding.api.ts", "./src/features/admindashboard/onboard/api/query.ts", "./src/features/admindashboard/onboard/components/admincolumn.tsx", "./src/features/admindashboard/onboard/components/onboardform.tsx", "./src/features/admindashboard/onboard/components/annonaotrcordiantorpasword.tsx", "./src/features/admindashboard/onboard/components/attendancetype.tsx", "./src/features/admindashboard/onboard/components/modal/deletemodal.tsx", "./src/features/admindashboard/onboard/components/modal/editmodal.tsx", "./src/features/admindashboard/onboard/components/modal/reactivemodal.tsx", "./src/features/admindashboard/onboard/components/modal/resetpasswordmodal.tsx", "./src/features/admindashboard/onboard/components/modal/suspendmodal.tsx", "./src/features/admindashboard/onboard/components/modal/updateform.tsx", "./src/features/admindashboard/packageadmin/index.tsx", "./src/features/admindashboard/packageadmin/api/api.tsx", "./src/features/admindashboard/packageadmin/api/query.tsx", "./src/features/admindashboard/packageadmin/component/features/featurespage.tsx", "./src/features/admindashboard/packageadmin/component/features/table/admincolumn.tsx", "./src/features/admindashboard/packageadmin/component/features/table/attendance.log.tsx", "./src/features/admindashboard/packageadmin/component/features/table/attendancetype.tsx", "./src/features/admindashboard/packageadmin/component/features/table/dummydata.ts", "./src/features/admindashboard/packageadmin/component/modal/deletefeatures.tsx", "./src/features/admindashboard/packageadmin/component/modal/deletepackage.tsx", "./src/features/admindashboard/packageadmin/component/modal/editfeatures.tsx", "./src/features/admindashboard/packageadmin/component/modal/editpackage.tsx", "./src/features/admindashboard/packageadmin/component/modal/featuresmodal.tsx", "./src/features/admindashboard/packageadmin/component/modal/packagemodal.tsx", "./src/features/admindashboard/packageadmin/component/package/packagepage.tsx", "./src/features/admindashboard/packageadmin/component/package/api_package/api_package.tsx", "./src/features/admindashboard/packageadmin/component/package/api_package/packagetype.ts", "./src/features/admindashboard/packageadmin/component/package/api_package/usepackagequery.ts", "./src/features/admindashboard/packageadmin/component/package/table/admincolumn.tsx", "./src/features/admindashboard/packageadmin/component/package/table/attendance.log.tsx", "./src/features/admindashboard/packageadmin/component/package/table/attendancetype.tsx", "./src/features/admindashboard/userall/index.tsx", "./src/features/admindashboard/userall/components/admincolumn.tsx", "./src/features/admindashboard/userall/components/attendance.log.tsx", "./src/features/admindashboard/userall/components/attendancetype.tsx", "./src/features/admindashboard/userall/components/formuserdetails/otpfailed.tsx", "./src/features/admindashboard/userall/components/formuserdetails/questionirefirst.tsx", "./src/features/admindashboard/userall/components/formuserdetails/userdetails.tsx", "./src/features/admindashboard/userall/components/formuserdetails/firstqustioniresubscription_api/firstsubscriptiondetails_api.tsx", "./src/features/admindashboard/userall/userall_api/useusersquery.ts", "./src/features/admindashboard/userall/userall_api/userapi.tsx", "./src/features/annotator/annotatorcolumn.tsx", "./src/features/annotator/annotatorcreatetask.tsx", "./src/features/annotator/annotatordashboard.tsx", "./src/features/annotator/annotatordescription.tsx", "./src/features/annotator/annotatordetails.tsx", "./src/features/annotator/annotatordraggable.tsx", "./src/features/annotator/index.tsx", "./src/features/annotator/updateannotatortask.tsx", "./src/features/annotator/updatedailytask.tsx", "./src/features/annotator/annonator_api/timglogannonator.tsx", "./src/features/annotator/annonator_api/annonator_api.tsx", "./src/features/annotator/annonator_api/annotator.mutation.ts", "./src/features/annotator/annonator_api/annotator.query.ts", "./src/features/annotator/annonator_api/user.query.ts", "./src/features/annotator/annotator-project-details/annotator_details_contain.tsx", "./src/features/annotator/annotator-project-details/annotator_details_draggable.tsx", "./src/features/annotator/annotator-project-details/annotator_details_kanban.tsx", "./src/features/annotator/annotator-project-details/annotator_details_task.tsx", "./src/features/annotator/annotator-project-details/annotator_details_update_task.tsx", "./src/features/annotator/annotator-project-details/api/api.tsx", "./src/features/annotator/dashboard/annotator.tsx", "./src/features/annotator/dashboard/attendence.tsx", "./src/features/annotator/dashboard/projectmangement.tsx", "./src/features/annotator/dashboard/annonator_notification/annonator_notification.tsx", "./src/features/annotator/dashboard/annonator_notification/annonatorleaveprrovalrejectfile.tsx", "./src/features/annotator/dashboard/annonator_notification/annonatorshiftaprrovalrejectfile.tsx", "./src/features/annotator/dashboard/annonator_notification/annotatornotification_api/annonatornotification_api.tsx", "./src/features/annotator/dashboard/component/annotatorcolumncontainer.tsx", "./src/features/annotator/dashboard/component/annotatorcreatetask.tsx", "./src/features/annotator/dashboard/component/annotatordraggable.tsx", "./src/features/annotator/dashboard/component/annotatorproject.tsx", "./src/features/annotator/dashboard/component/annotatorupdatetask.tsx", "./src/features/annotator/dashboard/component/clientslist.tsx", "./src/features/annotator/dashboard/component/coworkerlist.tsx", "./src/features/annotator/dashboard/component/topsection.tsx", "./src/features/annotator/dashboard/component/annonatorleave_api/annonatorleaveapi.tsx", "./src/features/annotator/dashboard/component/common/timein.tsx", "./src/features/annotator/dashboard/component/common/breakandleave.tsx", "./src/features/annotator/dashboard/component/common/requestleave.tsx", "./src/features/annotator/dashboard/component/modal/breakinout.tsx", "./src/features/annotator/dashboard/component/modal/clockinout.tsx", "./src/features/annotator/dashboard/component/modal/descriptionmodal.tsx", "./src/features/annotator/types/attendence.types.ts", "./src/features/annotator/types/attendencelog.types.ts", "./src/features/auth/auth.slice.ts", "./src/features/auth/type.ts", "./src/features/auth/api/annoatorblanpage.tsx", "./src/features/auth/api/auth-api.ts", "./src/features/auth/api/client-api.tsx", "./src/features/auth/api/getmatchmakingsubscriptioncheck.tsx", "./src/features/auth/formprofilechoose/index.tsx", "./src/features/auth/formprofilechoose/componets/authquestionformclient.tsx", "./src/features/auth/formprofilechoose/componets/formprofiledata.ts", "./src/features/auth/formprofilechoose/componets/packageplan.tsx", "./src/features/auth/formprofilechoose/componets/profileform.tsx", "./src/features/auth/formprofilechoose/formauth_api_all/formprofile_api.tsx", "./src/features/auth/formprofilechoose/signupclientquestionform/signupclientquestionform.tsx", "./src/features/auth/routes/logoutbar.tsx", "./src/features/auth/routes/bank-transfer.page.tsx", "./src/features/auth/routes/cowroker.signup.page.tsx", "./src/features/auth/routes/fogetpassword.tsx", "./src/features/auth/routes/index.tsx", "./src/features/auth/routes/login.tsx", "./src/features/auth/routes/otp.tsx", "./src/features/auth/routes/password.tsx", "./src/features/auth/routes/signup-otp.tsx", "./src/features/auth/routes/signup.page.tsx", "./src/features/auth/routes/signupotp.tsx", "./src/features/auth/routes/updatepassword.tsx", "./src/features/auth/routes/banktransfer-api/banktransfer-api.tsx", "./src/features/auth/routes/common/authcommon.tsx", "./src/features/auth/routes/common/authcommonalt.tsx", "./src/features/auth/routes/common/confirmationbutton.tsx", "./src/features/auth/routes/common/otpinput.tsx", "./src/features/auth/routes/common/recaptcha.tsx", "./src/features/auth/routes/common/addbank-transfer.tsx", "./src/features/auth/routes/common/file-uplaod.tsx", "./src/features/auth/routes/common/signupform.tsx", "./src/features/auth/routes/common/signupotpinput.tsx", "./src/features/auth/services/type.ts", "./src/features/chat/mediaselect.tsx", "./src/features/chat/apis/api.ts", "./src/features/chat/components/centerchat.tsx", "./src/features/chat/components/leftchat.tsx", "./src/features/chat/components/rightchat.tsx", "./src/features/chat/components/centerchat/chatheader.tsx", "./src/features/chat/components/centerchat/chatinput.tsx", "./src/features/chat/components/centerchat/messagelist.tsx", "./src/features/chat/components/centerchat/notificationdropdown.tsx", "./src/features/chat/components/centerchat/videocalldropdown.tsx", "./src/features/chat/components/centerchat/videocallinterigation.tsx", "./src/features/chat/components/rightchatcomponent/groupmembersname.tsx", "./src/features/chat/hooks/usechat.ts", "./src/features/chat/hooks/usejoindmroom.ts", "./src/features/chat/hooks/usejoingrouproom.ts", "./src/features/chat/routes/chat.tsx", "./src/features/chat/routes/index.tsx", "./src/features/chat/utils/messageutils.ts", "./src/features/clientdashboard/index.tsx", "./src/features/clientdashboard/add-on/api_addon/api_add.tsx", "./src/features/clientdashboard/add-on/component/addon.tsx", "./src/features/clientdashboard/add-on/component/blankdashboard.tsx", "./src/features/clientdashboard/add-on/component/commonpackageplan/togglesection.tsx", "./src/features/clientdashboard/add-on/component/commonpackageplan/addonaddress.checkout.tsx", "./src/features/clientdashboard/add-on/component/commonpackageplan/addonpackageplan.tsx", "./src/features/clientdashboard/add-on/component/commonpackageplan/addquestioniare.tsx", "./src/features/clientdashboard/add-on/component/commonpackageplan/address.checkout.tsx", "./src/features/clientdashboard/add-on/component/commonpackageplan/api_addon.tsx", "./src/features/clientdashboard/add-on/component/commonpackageplan/banktranferblankdashboard.tsx", "./src/features/clientdashboard/add-on/component/commonpackageplan/featurelabels.ts", "./src/features/clientdashboard/add-on/component/commonpackageplan/payment.selection.tsx", "./src/features/clientdashboard/add-on/component/commonpackageplan/questioniare.tsx", "./src/features/clientdashboard/add-on/route/index.tsx", "./src/features/clientdashboard/annoatorattendancehistory/attendace.type.tsx", "./src/features/clientdashboard/annoatorattendancehistory/attendance.log.tsx", "./src/features/clientdashboard/annoatorattendancehistory/column.tsx", "./src/features/clientdashboard/annoatorattendancehistory/annonatorhistory/annonatorhistory_api.tsx", "./src/features/clientdashboard/annoatorattendancehistory/annonatorhistory/annonatorquery.tsx", "./src/features/clientdashboard/annoatorattendancehistory/annonatorhistory/pdfeditordownloadhistory.tsx", "./src/features/clientdashboard/api/api.tsx", "./src/features/clientdashboard/api/mutation.ts", "./src/features/clientdashboard/api/query.ts", "./src/features/clientdashboard/billing/index.tsx", "./src/features/clientdashboard/billing/billing_api/billingapi.tsx", "./src/features/clientdashboard/billing/payment_method/paymentmethod.tsx", "./src/features/clientdashboard/billing/subscription/canclesubscription.tsx", "./src/features/clientdashboard/billing/subscription/index.tsx", "./src/features/clientdashboard/billing/transaction/downloadinvoicebutton.tsx", "./src/features/clientdashboard/billing/transaction/column.tsx", "./src/features/clientdashboard/billing/transaction/datatable.tsx", "./src/features/clientdashboard/billing/transaction/transaction.tsx", "./src/features/clientdashboard/billing/transaction/transactiontype.tsx", "./src/features/clientdashboard/clientnotification/clientleaveprrovalrejectfile.tsx", "./src/features/clientdashboard/clientnotification/clientshiftaprrovalrejectfile.tsx", "./src/features/clientdashboard/clientnotification/index.tsx", "./src/features/clientdashboard/clientnotification/clientnotification_api/adminnotification_api.tsx", "./src/features/clientdashboard/components/annotators.tsx", "./src/features/clientdashboard/components/clientcolumncontainer.tsx", "./src/features/clientdashboard/components/clientdraggabletask.tsx", "./src/features/clientdashboard/components/clientupdatetask.tsx", "./src/features/clientdashboard/components/createtask.tsx", "./src/features/clientdashboard/components/kanbandashboard.tsx", "./src/features/clientdashboard/components/olddashboard.tsx", "./src/features/clientdashboard/components/project.tsx", "./src/features/clientdashboard/components/questionarieform.tsx", "./src/features/clientdashboard/components/dashboard_api/dashboard_api.tsx", "./src/features/clientdashboard/dashboard/dashboard-page.tsx", "./src/features/clientdashboard/dashboard/routes/index.tsx", "./src/features/clientdashboard/invitecoworker/index.tsx", "./src/features/clientdashboard/invitecoworker/api/coworkers.api.ts", "./src/features/clientdashboard/invitecoworker/api/mutation.tsx", "./src/features/clientdashboard/invitecoworker/api/query.tsx", "./src/features/clientdashboard/invitecoworker/components/admincolumn.tsx", "./src/features/clientdashboard/invitecoworker/components/attendance.log.tsx", "./src/features/clientdashboard/invitecoworker/components/attendancetype.tsx", "./src/features/clientdashboard/invitecoworker/components/invitecoworkerform.tsx", "./src/features/clientdashboard/invitecoworker/components/permission.coworker.tsx", "./src/features/clientdashboard/payment-failed/index.tsx", "./src/features/clientdashboard/payment-success/index.tsx", "./src/features/clientdashboard/support/add-support-page.tsx", "./src/features/clientdashboard/support/add-support.tsx", "./src/features/clientdashboard/support/index.tsx", "./src/features/clientdashboard/support/api/zoho-desk-api.ts", "./src/features/clientdashboard/support/components/column.tsx", "./src/features/clientdashboard/support/components/data-table.tsx", "./src/features/clientdashboard/task-detail-page/annotatorcolumn.tsx", "./src/features/clientdashboard/task-detail-page/annotatorprojects.tsx", "./src/features/clientdashboard/task-detail-page/api/add-project-api.tsx", "./src/features/clientdashboard/task-detail-page/components/annotatordata.ts", "./src/features/clientdashboard/task-detail-page/components/annotatortype.tsx", "./src/features/clientdashboard/task-detail-page/components/taskdetails.tsx", "./src/features/clientdashboard/task-detail-page/components/annonator/annotatorcard.tsx", "./src/features/clientdashboard/task-detail-page/components/annonator/annotatorlist.tsx", "./src/features/clientdashboard/task-detail-page/components/annonator/annotatorstyles.ts", "./src/features/clientdashboard/task-detail-page/components/annonator/index.tsx", "./src/features/clientdashboard/task-detail-page/components/annonator/shiftchange.tsx", "./src/features/clientdashboard/task-detail-page/components/annonator/clientannonator_api/clientannonator_api.tsx", "./src/features/clientdashboard/task-detail-page/components/common/actionbuttons.tsx", "./src/features/clientdashboard/task-detail-page/components/common/cardstyles.ts", "./src/features/clientdashboard/task-detail-page/components/common/profilesection.tsx", "./src/features/clientdashboard/task-detail-page/components/coworker/index.tsx", "./src/features/clientdashboard/task-detail-page/components/coworker/componets/remove-page.tsx", "./src/features/clientdashboard/task-detail-page/components/project/projectcard.tsx", "./src/features/clientdashboard/task-detail-page/components/project/projectlist.tsx", "./src/features/clientdashboard/task-detail-page/components/project/index.tsx", "./src/features/clientdashboard/task-detail-page/components/project/components/questionnaire.tsx", "./src/features/clientdashboard/task-detail-page/components/project/components/project-create.tsx", "./src/features/clientdashboard/task-detail-page/components/project/project_api/project_api.tsx", "./src/features/clientdashboard/task-detail-page/components/project/project_api/useclientprojects.ts", "./src/features/clientdashboard/task-detail-page/components/project/styles/projectcreatestyles.ts", "./src/features/clientdashboard/task-detail-page/components/projectdetails/columncontainertask.tsx", "./src/features/clientdashboard/task-detail-page/components/projectdetails/createprojecttask.tsx", "./src/features/clientdashboard/task-detail-page/components/projectdetails/draggabletask.tsx", "./src/features/clientdashboard/task-detail-page/components/projectdetails/projectdetails.tsx", "./src/features/clientdashboard/task-detail-page/components/projectdetails/projectkanban.tsx", "./src/features/clientdashboard/task-detail-page/components/projectdetails/updateprojecttask.tsx", "./src/features/clientdashboard/task-detail-page/components/projectdetails/componentsproejctdetails/annotatordetails.tsx", "./src/features/clientdashboard/task-detail-page/components/projectdetails/componentsproejctdetails/clientsproejctdescription.tsx", "./src/features/clientdashboard/task-detail-page/components/projectdetails/componentsproejctdetails/coworkerdetails.tsx", "./src/features/clientdashboard/task-detail-page/components/projectdetails/componentsproejctdetails/taskrecord.tsx", "./src/features/clientdashboard/task-detail-page/components/projectdetails/projectdetails_api/clientprojectupdate.tsx", "./src/features/clientdashboard/task-detail-page/components/projectdetails/projectdetails_api/projectdetails_api.tsx", "./src/features/clientdashboard/task-detail-page/components/styles/projectcardstyles.ts", "./src/features/clientdashboard/task-detail-page/routes/index.tsx", "./src/features/layout/text.tsx", "./src/features/layout/top-navbar.tsx", "./src/features/layout/clientprofile/index.tsx", "./src/features/layout/clientprofile/component/admiinand_otherprofile.tsx", "./src/features/layout/clientprofile/component/billingdata.tsx", "./src/features/layout/clientprofile/component/profileuseralldata.tsx", "./src/features/layout/clientprofile/profileclient_api/clientprofile_api.tsx", "./src/features/layout/clientprofile/profileclient_api/adminother_api.tsx", "./src/features/layout/dashboard/newdahboard-layout.tsx", "./src/features/layout/dashboard/sidebar-newdata.tsx", "./src/features/layout/dashboard/components/sidebar.tsx", "./src/features/layout/dashboard/components/sidebaritem.tsx", "./src/features/layout/dashboard/components/sidebarsection.tsx", "./src/features/projectcordinator/coordinatortype.tsx", "./src/features/projectcordinator/annotatordetails.tsx", "./src/features/projectcordinator/clientcoordinator.tsx", "./src/features/projectcordinator/coordinatorcolumn.tsx", "./src/features/projectcordinator/coordinatorcoworker.tsx", "./src/features/projectcordinator/coordinatordata.ts", "./src/features/projectcordinator/coordinatordetails.tsx", "./src/features/projectcordinator/coordinatordraggable.tsx", "./src/features/projectcordinator/coordinatorkanban.tsx", "./src/features/projectcordinator/coordinatortask.tsx", "./src/features/projectcordinator/coordinatorupdate.tsx", "./src/features/projectcordinator/index.tsx", "./src/features/projectcordinator/annotatorprojects/annotatorcolumn.tsx", "./src/features/projectcordinator/annotatorprojects/index.tsx", "./src/features/projectcordinator/api/api.ts", "./src/features/projectcordinator/api/mutation.tsx", "./src/features/projectcordinator/api/query.tsx", "./src/features/projectcordinator/api/usecoordinatorclientprojects.ts", "./src/features/projectcordinator/api/usecoordinatorprojects.ts", "./src/features/projectcordinator/clientprojects/clientcolumn.tsx", "./src/features/projectcordinator/clientprojects/index.tsx", "./src/features/projectcordinator/coordinator-projects-details/coordinator_details_contain.tsx", "./src/features/projectcordinator/coordinator-projects-details/coordinator_details_draggable.tsx", "./src/features/projectcordinator/coordinator-projects-details/coordinator_details_task.tsx", "./src/features/projectcordinator/coordinator-projects-details/coordinator_detials_update_task.tsx", "./src/features/projectcordinator/coordinator-projects-details/coordintator_details_kan.tsx", "./src/features/projectcordinator/coordinator-projects-details/api/api.tsx", "./src/features/projectcordinator/cordinayordetailscard/admintaskdetails.tsx", "./src/features/projectcordinator/cordinayordetailscard/annonatorcoordinator/coordinatorshiftchange.tsx", "./src/features/projectcordinator/cordinayordetailscard/annonatorcoordinator/index.tsx", "./src/features/projectcordinator/cordinayordetailscard/annonatorcoordinator/coordinatorshift_api/coordinatorshift_api.tsx", "./src/features/projectcordinator/cordinayordetailscard/clientadmin/coordinatorcolumn.tsx", "./src/features/projectcordinator/cordinayordetailscard/clientadmin/coordinatorprojects.tsx", "./src/features/projectcordinator/cordinayordetailscard/clientadmin/index.tsx", "./src/features/projectcordinator/cordinayordetailscard/clientadmin/components/admincolumn.tsx", "./src/features/projectcordinator/cordinayordetailscard/clientadmin/components/attendance.log.tsx", "./src/features/projectcordinator/cordinayordetailscard/clientadmin/components/attendancetype.tsx", "./src/features/projectcordinator/cordinayordetailscard/clientadmin/components/dummydata.ts", "./src/features/projectcordinator/cordinayordetailscard/clientadmin/components/index.tsx", "./src/features/projectcordinator/cordinayordetailscard/projectadmin/index.tsx", "./src/features/projectcordinator/dashboard/coordinatorcolumncontainer.tsx", "./src/features/projectcordinator/dashboard/coordinatorcreatetask.tsx", "./src/features/projectcordinator/dashboard/coordinatordraggable.tsx", "./src/features/projectcordinator/dashboard/coordinatorkanban.tsx", "./src/features/projectcordinator/dashboard/coordinatorupdatetask.tsx", "./src/features/projectcordinator/dashboard/projectcoardinator.tsx", "./src/features/projectcordinator/dashboard/projectcoardinatordetails.tsx", "./src/features/projectcordinator/dashboard/components/topsectiondashboard.tsx", "./src/features/projectcordinator/dashboard/components/common/clientlist.tsx", "./src/features/projectcordinator/dashboard/components/common/coordinator-annotator.tsx", "./src/features/projectcordinator/dashboard/components/common/coordinator-project.tsx", "./src/features/projectcordinator/notification/shiftnotification.tsx", "./src/features/projectcordinator/notification/notification.tsx", "./src/features/projectcordinator/notification/leavenotificationfoler/leave_api/leaveannonator_api.tsx", "./src/features/projectcordinator/notification/leavenotificationfoler/leavecomponent/leavenotification.tsx", "./src/features/projectcordinator/notification/notification_api/notification_api.tsx", "./src/features/projectcordinator/projectdetailsapi/detailsapi.tsx", "./src/features/projectcordinator/types/client.types.ts", "./src/features/settings/faqs/index.tsx", "./src/features/settings/route/index.tsx", "./src/features/settings/setting-file/index.tsx", "./src/features/settings/setting-file/components/confimmodal.tsx", "./src/features/settings/setting-file/components/delete.tsx", "./src/features/settings/setting-file/components/otpmodal.tsx", "./src/globalurl/baseurl.tsx", "./src/hooks/use-mobile.tsx", "./src/hooks/use-responsive.tsx", "./src/hooks/use-toast.ts", "./src/lib/authredirect.tsx", "./src/lib/coworkerinviteredirect.tsx", "./src/lib/utils.ts", "./src/routes/index.tsx", "./src/routes/private.route.tsx", "./src/routes/protected.route.tsx", "./src/routes/publicroute.tsx", "./src/routes/router.tsx", "./src/socket/socket.tsx", "./src/socket/usesocket.ts", "./src/store/index.ts", "./src/store/dicebearname/getavatarurl.ts", "./src/store/hooks/reduxhooks.ts", "./src/store/slices/authslice.ts", "./src/store/slices/userslice.ts", "./src/types/admincreate.types.ts", "./src/types/adminkanbantype.ts", "./src/types/generics.ts", "./src/types/kanbantasktype.ts", "./src/types/matchmaking.types.ts", "./src/types/onboarding.types.ts", "./src/utils/errorpage.tsx", "./src/utils/permissiongate.tsx", "./src/utils/axio-interceptor.ts", "./src/utils/constants.ts", "./src/utils/countries.tsx", "./src/utils/errorboundry.tsx", "./src/utils/errorhandler.ts", "./src/utils/generateattendancepdf.tsx", "./src/utils/generateinvoicepdf.tsx", "./src/utils/helper.ts", "./src/utils/navigaterole.tsx", "./src/utils/permissions.ts", "./src/utils/role-checks.tsx"], "version": "5.6.3"}