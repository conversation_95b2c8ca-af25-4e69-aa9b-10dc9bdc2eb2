// import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-toastify"; // if you are using react-toastify
// // wherever you handle errors
// import { createUser, resetPassword, suspendUser } from "./matchmaking.api";
// import { errorMessage } from "@/utils/errorHandler";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { errorMessage } from "@/utils/errorHandler";
import { createSubscription } from "./api";
import { cancelSubscription } from "../billing/billing_api/billingapi";
import { useNavigate } from "react-router-dom";

// interface CreateUserInput {
//   name: string;
//   email: string;
//   domain: string;
//   role: string;
//   packageId: string;
//   password?: string | null;
// }

// export function useAddUserMutation() {
//   const queryClient = useQueryClient();

//   return useMutation({
//     mutationFn: (content: CreateUserInput) => createUser(content),
//     onSettled: async (_data, error) => {
//       if (error) {
//         console.error(error);
//         if ((error as any)?.response?.data) {
//           errorMessage(error);
//         }
//       } else {
//         toast.success("User added successfully");
//         await queryClient.invalidateQueries({ queryKey: ["packages"] }); // Update queryKey accordingly
//       }
//     },
//   });
// }

// export function useResetPasswordMutation() {
//   const queryClient = useQueryClient();

//   return useMutation({
//     mutationFn: (content: { id: string; password: string | null }) =>
//       resetPassword(content),
//     onSettled: async (_data, error) => {
//       if (error) {
//         console.error(error);
//         if ((error as any)?.response?.data) {
//           errorMessage(error);
//         }
//       } else {
//         toast.success("Password reset successfully");
//         await queryClient.invalidateQueries({ queryKey: ["packages"] }); // Update queryKey accordingly
//       }
//     },
//   });
// }

// export function useSuspendUserMutation() {
//   const queryClient = useQueryClient();

//   return useMutation({
//     mutationFn: (content: { id: string; suspendedUntil: number | null }) =>
//       suspendUser(content),
//     onSettled: async (_data, error) => {
//       if (error) {
//         console.error(error);
//         if ((error as any)?.response?.data) {
//           errorMessage(error);
//         }
//       } else {
//         toast.success("User suspended successfully");
//         await queryClient.invalidateQueries({ queryKey: ["packages"] }); // Update queryKey accordingly
//       }
//     },
//   });
// }

export function useCreateSubscriptionMutation() {
  // const queryClient = useQueryClient();
  const navigate = useNavigate();

  return useMutation({
    mutationFn: (content: {
      availableFrom: string | null;
      availableTo: string | null;
      packageId: string | null | undefined;
      paymentMethod: string;
      timezone: string;
      industry: string;
      category: string;
      description?: string;
      startOn?: string
      billing: {
        street: string;
        city: string;
        state: string;
        zipcode: string;
        country: string;
      };
    }) => createSubscription(content),
    onSuccess: async (data) => {
      if (data?.payment_link) {
        window.location.href = data.payment_link;
      } else {
        navigate("/dashboard");
      }

      // Optionally refetch other queries
      // await queryClient.invalidateQueries({ queryKey: ["matchmaking"] });
      // await queryClient.invalidateQueries({ queryKey: ["packages"] });
    },
    onSettled: async (_data, error) => {
      if (error) {
        console.error(error);
        if ((error as any)?.response?.data) {
          errorMessage(error);
        }
      } else {
        toast.success("Subscription created successfully");
        // Invalidate both matchmaking and packages queries to refresh the data
        // await queryClient.invalidateQueries({ queryKey: ["matchmaking"] });
        // await queryClient.invalidateQueries({ queryKey: ["packages"] });
      }
    },
  });
}

export function useCancelSubscriptionMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (subscriptionId: string) => cancelSubscription(subscriptionId),
    onSuccess: async () => {
      toast.success("Subscription cancelled successfully");
      // Invalidate subscription-related queries to refresh the data
      await queryClient.invalidateQueries({ queryKey: ["subscriptions"] });
      await queryClient.invalidateQueries({ queryKey: ["billing"] });
    },
    onError: (error) => {
      console.error("Error cancelling subscription:", error);
      if ((error as any)?.response?.data) {
        errorMessage(error);
      } else {
        toast.error("Failed to cancel subscription. Please try again.");
      }
    },
  });
}
