import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useCancelSubscriptionMutation } from "../../api/mutation";

interface CancelSubscriptionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm?: () => void;
  subscriptionName: string;
  subscriptionId: string;
}

export default function CancelSubscriptionDialog({
  isOpen,
  onClose,
  onConfirm,
  subscriptionName,
  subscriptionId,
}: CancelSubscriptionDialogProps) {
  const cancelSubscriptionMutation = useCancelSubscriptionMutation();

  const handleCancelSubscription = async () => {
    try {
      await cancelSubscriptionMutation.mutateAsync(subscriptionId);
      onClose();
      if (onConfirm) {
        onConfirm();
      }
    } catch (error) {
      // Error is handled by the mutation hook
      console.error("Failed to cancel subscription:", error);
    }
  };
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="text-[#FF577F] text-[22px]">
            Cancel Subscription
          </DialogTitle>
        </DialogHeader>
        
        <div className="py-4">
          <p className="text-gray-700">
            Are you sure you want to cancel your{" "}
            <span className="font-semibold">{subscriptionName}</span> subscription?
          </p>
          <p className="text-sm text-gray-500 mt-2">
            Your subscription will remain active until the end of the current billing period.
          </p>
        </div>
        
        <DialogFooter className="gap-2 sm:gap-0">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={cancelSubscriptionMutation.isPending}
          >
            No, Keep Subscription
          </Button>
          <Button
            type="button"
            className="bg-[#FF577F] hover:bg-[#E54A6D]"
            onClick={handleCancelSubscription}
            disabled={cancelSubscriptionMutation.isPending}
          >
            {cancelSubscriptionMutation.isPending ? "Cancelling..." : "Yes, Cancel Subscription"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}