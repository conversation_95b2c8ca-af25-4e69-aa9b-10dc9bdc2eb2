// "use client";

// import { useState } from "react";
// import { Button } from "@/components/ui/button";
// import { FaDownload, FaSpinner } from "react-icons/fa";
// // import { getInvoiceData } from "../billing_api/billingapi";
// import { generateInvoicePdf } from "@/utils/generateInvoicePdf";

// interface DownloadInvoiceButtonProps {
//   paymentId: string;
//   customerName: string;
//   customerEmail: string;
//   packageName: string;
//   amount: number;
//   status: string;
//   paymentMethod: string;
//   date: string;
// }

// export const DownloadInvoiceButton = ({
//   paymentId,
//   customerName,
//   customerEmail,
//   packageName,
//   amount,
//   status,
//   paymentMethod,
//   date
// }: DownloadInvoiceButtonProps) => {
//   const [isLoading, setIsLoading] = useState(false);

//   const handleDownload = async () => {
//     setIsLoading(true);
//     try {
//     cons
//     } catch (error) {
//       console.error("Download error:", error);
//       alert(`Failed to download: ${error instanceof Error ? error.message : "Unknown error"}`);
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   return (
//     <Button 
//       variant="ghost" 
//       onClick={handleDownload}
//       disabled={isLoading}
//       className="text-[20px] text-[#5f5f5f] hover:text-[#37cf6a] cursor-pointer"
//     >
//       {isLoading ? <FaSpinner className="animate-spin" /> : <FaDownload />}
//     </Button>
//   );
// };
"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { FaDownload, FaSpinner } from "react-icons/fa";
import { customAxios } from "@/utils/axio-interceptor";

interface DownloadInvoiceButtonProps {
  paymentId: string;
}

export const DownloadInvoiceButton = ({ paymentId }: DownloadInvoiceButtonProps) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleDownload = async () => {
    setIsLoading(true);
    try {
      const response = await customAxios.get(`/v1/billings/invoice/${paymentId}`, {
        responseType: "blob", // very important for PDF buffer
      });

      const blob = new Blob([response.data], { type: response.headers["content-type"] });
      let fileName = `invoice-${paymentId}.pdf`;

      // Extract filename from Content-Disposition header if available
      const contentDisposition = response.headers["content-disposition"];
      if (contentDisposition) {
        const match = contentDisposition.match(/filename="?([^"]+)"?/);
        if (match && match[1]) fileName = match[1];
      }

      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);

    } catch (error: any) {
      console.error("Download error:", error);
      alert(`Failed to download: ${error.message || "Unknown error"}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      variant="ghost"
      onClick={handleDownload}
      disabled={isLoading}
      className="text-[20px] text-[#5f5f5f] hover:text-[#37cf6a] cursor-pointer"
    >
      {isLoading ? <FaSpinner className="animate-spin" /> : <FaDownload />}
    </Button>
  );
};
