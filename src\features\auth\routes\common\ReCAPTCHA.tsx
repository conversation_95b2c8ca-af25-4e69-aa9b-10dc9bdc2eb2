// components/ReCAPTCHAComponent.tsx
import React, { Dispatch, SetStateAction } from "react";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "react-google-recaptcha";

interface ReCAPTCHAProps {
  setCaptchaVerified: Dispatch<SetStateAction<boolean>>;
}

const ReCAPTCHAComponent: React.FC<ReCAPTCHAProps> = ({
  setCaptchaVerified,
}) => {
  const siteKey = import.meta.env.VITE_RECAPTCHA_SITE_KEY as string;

  console.log("siteKey", siteKey);

  const onChange = (value: string | null) => {
    setCaptchaVerified(!!value);
  };

  return (
    <div className="mt-4">
      <ReCAPTCHA sitekey={siteKey} onChange={onChange} />
    </div>
  );
};

export default ReCAPTCHAComponent;
