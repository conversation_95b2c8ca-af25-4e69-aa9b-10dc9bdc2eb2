import React from "react";
import logo from "@/assets/darklogo.png";

interface InvoiceTemplateProps {
  invoiceData: {
    paymentId: string;
    amount: string;
    date: string;
    customerName: string;
    customerEmail: string;
    packageName: string;
    price: string;
  };
}

export const InvoiceTemplate: React.FC<InvoiceTemplateProps> = ({
  invoiceData,
}) => {
  const generateInvoiceNumber = () => {
    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const nextYear = (date.getFullYear() + 1).toString().slice(-2);
    const randomNum = Math.floor(Math.random() * 99999)
      .toString()
      .padStart(5, "0");
    return `INV${year}/${nextYear}-${randomNum}`;
  };

  const convertAmountToWords = (amount: string) => {
    const num = parseInt(amount);
    if (num === 300) return 'United States Dollar Three Hundred';
    return `United States Dollar ${num}`; // Replace with number-to-words in production
  };

  return (
    <div className="w-[210mm] min-h-[297mm] bg-white p-8 font-sans text-black" style={{ fontSize: '11px' }}>
      {/* Print CSS */}
      <style>
        {`
          @media print {
            img {
              display: block !important;
              width: 50px !important;
              height: 50px !important;
            }
            .no-print {
              display: none !important;
            }
          }
        `}
      </style>

      {/* Header */}
      <div className="flex justify-between mb-8">
        <div>
          <img
            src={logo}
            alt="get Annotator logo"
            className="w-[185px] h-[75px]"
          />
          {/* <div className="text-xl font-bold">GetAnnotator</div> */}
          <div className="text-xs text-gray-600 space-y-1">
            <div className="font-semibold text-gray-800">Macgence Technologies Private Limited</div>
            <div>CN UF22000UP2022PTC164392</div>
            <div>7th Floor, Platina Heights C - 24 Sector 62</div>
            <div>Noida Uttar Pradesh 201301</div>
            <div>India</div>
            <div className="font-medium">GSTIN: 09AAPCH4735A1Z9</div>
          </div>
        </div>
        <div className="text-right">
          <h1 className="text-2xl font-bold text-[#BE3930] mb-4">TAX INVOICE</h1>
          <div className="text-sm">
            <div className="font-semibold">Invoice# {generateInvoiceNumber()}</div>
            <div className="mt-2">
              <span className="font-semibold">Balance Due: </span>
              <span className="font-bold text-[#BE3930]">${invoiceData.amount}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Bill To & Dates */}
      <div className="flex justify-between mb-8">
        <div className="text-sm">
          <div className="font-bold">Bill To</div>
          <div className="font-semibold">{invoiceData.customerName}</div>
          <div className="text-gray-600">{invoiceData.customerEmail}</div>
          <div className="text-gray-600">Israel</div>
        </div>
        <div className="text-sm space-y-2">
          <div className="flex justify-between min-w-[200px]">
            <span className="text-gray-600">Invoice Date:</span>
            <span>{new Date(invoiceData.date).toLocaleDateString('en-GB')}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Terms:</span>
            <span>Net 4</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Due Date:</span>
            <span>
              {new Date(new Date(invoiceData.date).getTime() + 4 * 24 * 60 * 60 * 1000).toLocaleDateString('en-GB')}
            </span>
          </div>
        </div>
      </div>

      {/* Table */}
      <table className="w-full border-collapse mb-8">
        <thead>
          <tr className="bg-[#BE3930] text-white">
            <th className="p-2 text-left text-xs font-bold w-8">#</th>
            <th className="p-2 text-left text-xs font-bold">Item & Description</th>
            <th className="p-2 text-left text-xs font-bold w-20">HSN/SAC</th>
            <th className="p-2 text-left text-xs font-bold w-20">Qty</th>
            <th className="p-2 text-left text-xs font-bold w-16">Rate</th>
            <th className="p-2 text-left text-xs font-bold w-20">Amount</th>
          </tr>
        </thead>
        <tbody>
          <tr className="border-b border-gray-300">
            <td className="p-2 text-xs">1</td>
            <td className="p-2 text-xs">
              <div className="font-medium">{invoiceData.packageName}</div>
              <div className="text-gray-500">(Professional Annotation Services)</div>
            </td>
            <td className="p-2 text-xs">998313</td>
            <td className="p-2 text-xs">1.00</td>
            <td className="p-2 text-xs">${invoiceData.amount}</td>
            <td className="p-2 text-xs font-medium">${invoiceData.amount}</td>
          </tr>
        </tbody>
      </table>

      {/* Totals */}
      <div className="flex justify-end mb-8">
        <div className="w-64 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">Sub Total</span>
            <span>${invoiceData.amount}</span>
          </div>
          <div className="flex justify-between font-bold mt-2">
            <span>Total</span>
            <span className="text-blue-600">${invoiceData.amount}</span>
          </div>
          <div className="mt-4 italic text-blue-600">{convertAmountToWords(invoiceData.amount)}</div>
        </div>
      </div>

      {/* Footer */}
      <div className="border-t pt-4 text-xs">
        <div className="font-semibold">Thanks for your business!</div>
        <div className="mt-2">
          <span className="font-semibold">PayPal account:</span> <EMAIL>
        </div>
        <div className="grid grid-cols-2 gap-4 mt-2">
          <div>
            <div className="font-semibold">Account Details:</div>
            <div>Account Holder: Macgence Technologies Private Limited</div>
            <div>Payment Method: SWIFT (International Wire)</div>
            <div>IBAN: **********************</div>
            <div>BIC/SWIFT: TCCLGB3L</div>
          </div>
          <div>
            <div className="font-semibold">Bank Details:</div>
            <div>Bank Name: The Currency Cloud Limited</div>
            <div>Bank Address: 12, Steward Street,</div>
            <div>The Steward Building, London, E1 6FQ,</div>
            <div>Great Britain, United Kingdom</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InvoiceTemplate;